part of '../wu_backup.dart';

typedef PrepareBackupFiles = Future<List<String>> Function(String tempDirPath);
typedef OnFileRestored = void Function(String restoredFilePath);

class ZipBackup {
  final String backupPath;
  final String zipFileName;

  ZipBackup(this.backupPath, this.zipFileName);

  /// 將所要備份的資料保存至暫存資料夾，然後壓縮成 zip 檔案，然後再將 zip 檔案複製到指定的備份資料夾中
  Future<void> backup(PrepareBackupFiles prepareBackupFiles) async {
    // 靜默請求存儲權限
    final hasPermission = await PermissionHelper.requestStoragePermissionSilently();

    final tempDir = await getTemporaryDirectory();
    final zipFilePath = '${tempDir.path}/$zipFileName';

    // 將所有要備份的檔案保存至暫存資料夾
    final backupFiles = await prepareBackupFiles(tempDir.path);

    // 將所有檔案打包成 zip 檔案，並將 zip 檔案寫入暫存資料夾
    final archive = Archive();
    for (final filename in backupFiles) {
      final file = File('${tempDir.path}/$filename');
      if (!file.existsSync()) {
        throw Exception('備份檔案不存在: $filename');
      }
      final bytes = file.readAsBytesSync();
      final name = file.path.split('/').last;
      archive.addFile(ArchiveFile(name, bytes.length, bytes));
    }
    final encoder = ZipEncoder();
    final zipBytes = encoder.encode(archive);
    await File(zipFilePath).writeAsBytes(zipBytes);

    // 確定最終備份路徑
    String finalBackupDir;
    if (hasPermission && await PermissionHelper.isDirectoryWritable(backupPath)) {
      // 如果有權限且指定目錄可寫，使用指定目錄
      finalBackupDir = backupPath;
    } else {
      // 否則使用安全的備份目錄
      finalBackupDir = await PermissionHelper.getSafeBackupDirectory();
    }

    // 確保備份資料夾存在
    final backupDir = Directory(finalBackupDir);
    if (!backupDir.existsSync()) {
      await backupDir.create(recursive: true);
    }

    // 將 zip 檔案複製到最終備份資料夾中
    final finalBackupPath = '$finalBackupDir/$zipFileName';
    await File(zipFilePath).copy(finalBackupPath);

    // 如果使用了安全目錄，更新 backupPath 以便後續使用
    if (finalBackupDir != backupPath) {
      // 這裡可以通過回調或其他方式通知調用者實際使用的路徑
      print('備份已保存到安全目錄: $finalBackupPath');
    }
  }

  /// 自動還原最新的備份檔案（不顯示對話框）
  Future<void> restore(OnFileRestored onFileRestored) async {
    // 靜默請求存儲權限
    await PermissionHelper.requestStoragePermissionSilently();
    final zipFilePath = '$backupPath/$zipFileName';
    final zipFile = File(zipFilePath);
    if (!zipFile.existsSync()) {
      throw Exception('備份檔案不存在: $zipFileName');
    }

    // 將 zip 檔案解壓縮到暫存資料夾
    final tempDir = await getTemporaryDirectory();
    final bytes = zipFile.readAsBytesSync();
    final archive = ZipDecoder().decodeBytes(bytes);

    for (final file in archive) {
      final filename = file.name;
      final data = file.content as List<int>;

      // 將檔案解壓縮到暫存資料夾
      final tempFilePath = '${tempDir.path}/$filename';
      await File(tempFilePath).writeAsBytes(data);

      // 呼叫回調函式，傳入暫存資料夾中的檔案路徑
      onFileRestored(tempFilePath);
    }
  }
}
